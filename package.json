{"name": "electron-calendar-app", "private": true, "version": "0.0.0", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "concurrently \"vite\" \"wait-on tcp:5173 && electron .\"", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron": "electron .", "electron:pack": "electron-builder", "electron:dev": "concurrently \"vite\" \"wait-on tcp:5173 && electron .\"", "electron:preview": "npm run build && electron-builder --publish=never", "pree2e": "npm run build", "e2e": "playwright test"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^30.0.0", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "wait-on": "^7.2.0"}, "build": {"appId": "com.example.calendar", "productName": "Calendar App", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*"], "mac": {"icon": "assets/icon.icns"}, "win": {"icon": "assets/icon.ico"}, "linux": {"icon": "assets/icon.png"}}}